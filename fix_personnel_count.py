#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
إصلاح مشكلة عدد الأفراد في المواقع
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import app
from locations import get_db_connection

def fix_personnel_count():
    """إصلاح مشكلة عدد الأفراد في المواقع"""
    
    with app.app_context():
        print("🔧 إصلاح مشكلة عدد الأفراد في المواقع...")
        print("=" * 50)
        
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # البحث عن الأفراد المرتبطين بمواقع لكن لا يوجدون في جدول personnel
        print("🔍 البحث عن الأفراد المرتبطين بمواقع لكن غير موجودين في جدول personnel...")
        cursor.execute("""
            SELECT lp.location_id, lp.personnel_id, l.name as location_name
            FROM location_personnel lp
            JOIN locations l ON lp.location_id = l.id
            LEFT JOIN personnel p ON lp.personnel_id = p.id
            WHERE lp.is_active = 1 AND p.id IS NULL
        """)
        
        orphaned_records = cursor.fetchall()
        
        if orphaned_records:
            print(f"❌ تم العثور على {len(orphaned_records)} سجل أفراد مرتبط بمواقع لكن الفرد غير موجود:")
            for record in orphaned_records:
                print(f"  - الموقع: {record['location_name']} (ID: {record['location_id']}) - الفرد: {record['personnel_id']}")
            
            # حذف السجلات المعطلة
            print("\n🗑️ حذف السجلات المعطلة...")
            cursor.execute("""
                DELETE FROM location_personnel 
                WHERE personnel_id NOT IN (SELECT id FROM personnel)
            """)
            deleted_count = cursor.rowcount
            print(f"✅ تم حذف {deleted_count} سجل معطل")
        else:
            print("✅ لا توجد سجلات معطلة")
        
        # عرض إحصائيات المواقع بعد التنظيف
        print("\n📊 إحصائيات المواقع بعد التنظيف:")
        cursor.execute("""
            SELECT l.id, l.name,
                   COUNT(DISTINCT le.id) as equipment_count,
                   COUNT(DISTINCT CASE WHEN lp.is_active = 1 AND p.id IS NOT NULL THEN lp.personnel_id END) as personnel_count
            FROM locations l
            LEFT JOIN location_equipment le ON l.id = le.location_id
            LEFT JOIN location_personnel lp ON l.id = lp.location_id
            LEFT JOIN personnel p ON lp.personnel_id = p.id
            GROUP BY l.id, l.name
            ORDER BY personnel_count DESC, l.name
        """)
        
        locations = cursor.fetchall()
        
        total_locations = len(locations)
        locations_with_personnel = sum(1 for loc in locations if loc['personnel_count'] > 0)
        total_personnel = sum(loc['personnel_count'] for loc in locations)
        
        print(f"📍 إجمالي المواقع: {total_locations}")
        print(f"👥 المواقع التي تحتوي على أفراد: {locations_with_personnel}")
        print(f"👤 إجمالي الأفراد المرتبطين: {total_personnel}")
        
        if locations_with_personnel > 0:
            print("\n📋 تفاصيل المواقع التي تحتوي على أفراد:")
            for location in locations:
                if location['personnel_count'] > 0:
                    print(f"  - {location['name']} (ID: {location['id']}): {location['personnel_count']} فرد، {location['equipment_count']} عهدة")
        
        # التحقق من الأفراد المكررين في نفس الموقع
        print("\n🔍 البحث عن الأفراد المكررين في نفس الموقع...")
        cursor.execute("""
            SELECT location_id, personnel_id, COUNT(*) as count
            FROM location_personnel
            WHERE is_active = 1
            GROUP BY location_id, personnel_id
            HAVING count > 1
        """)
        
        duplicates = cursor.fetchall()
        if duplicates:
            print(f"❌ تم العثور على {len(duplicates)} حالة تكرار:")
            for dup in duplicates:
                print(f"  - الموقع {dup['location_id']} - الفرد {dup['personnel_id']} - {dup['count']} مرات")
            
            # إصلاح التكرارات
            print("\n🔧 إصلاح التكرارات...")
            for dup in duplicates:
                # الاحتفاظ بأحدث سجل وحذف الباقي
                cursor.execute("""
                    DELETE FROM location_personnel 
                    WHERE location_id = ? AND personnel_id = ? AND is_active = 1
                    AND id NOT IN (
                        SELECT id FROM location_personnel 
                        WHERE location_id = ? AND personnel_id = ? AND is_active = 1
                        ORDER BY created_at DESC 
                        LIMIT 1
                    )
                """, (dup['location_id'], dup['personnel_id'], dup['location_id'], dup['personnel_id']))
            
            print(f"✅ تم إصلاح جميع التكرارات")
        else:
            print("✅ لا توجد تكرارات")
        
        conn.commit()
        conn.close()
        
        print("\n✅ تم إصلاح مشكلة عدد الأفراد بنجاح!")
        print("🔄 يرجى إعادة تحميل صفحة المواقع لرؤية التحديثات")

if __name__ == '__main__':
    fix_personnel_count()
