{% extends "base.html" %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-8">
        <h3>
            {% if stat_type == 'warehouses' %}
            إحصائيات المستودعات
            {% elif stat_type == 'weapons' %}
            إحصائيات الأسلحة
            {% elif stat_type == 'personnel' %}
            إحصائيات الأفراد
            {% elif stat_type == 'devices' %}
            إحصائيات الأجهزة
            {% endif %}
        </h3>
    </div>
    <div class="col-md-4 text-right">
        <a href="{{ url_for('reports.index') }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-right"></i> العودة إلى التقارير
        </a>
    </div>
</div>

{% if stat_type == 'warehouses' %}
<!-- Warehouse Statistics -->
<div class="row">
    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-warehouse"></i> مقارنة المستودعات - الأسلحة</h5>
            </div>
            <div class="card-body">
                <div class="chart-container" style="position: relative; height: 300px;">
                    <canvas id="warehouseWeaponsChart"></canvas>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-users"></i> مقارنة المستودعات - الأفراد</h5>
            </div>
            <div class="card-body">
                <div class="chart-container" style="position: relative; height: 300px;">
                    <canvas id="warehousePersonnelChart"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    {% for warehouse in data.warehouse_stats %}
    <div class="col-md-4 mb-4">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-chart-pie"></i> {{ warehouse.name }} - حالة الأسلحة</h5>
            </div>
            <div class="card-body">
                <div class="chart-container" style="position: relative; height: 250px;">
                    <canvas id="weaponStatus{{ loop.index }}Chart"></canvas>
                </div>
            </div>
        </div>
    </div>
    {% endfor %}
</div>

{% elif stat_type == 'weapons' %}
<!-- Weapon Statistics -->
<div class="row">
    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-warehouse"></i> توزيع الأسلحة حسب المستودع</h5>
            </div>
            <div class="card-body">
                <div class="chart-container" style="position: relative; height: 300px;">
                    <canvas id="weaponsByWarehouseChart"></canvas>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-crosshairs"></i> توزيع الأسلحة حسب النوع</h5>
            </div>
            <div class="card-body">
                <div class="chart-container" style="position: relative; height: 300px;">
                    <canvas id="weaponsByTypeChart"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-chart-pie"></i> توزيع الأسلحة حسب الحالة</h5>
            </div>
            <div class="card-body">
                <div class="chart-container" style="position: relative; height: 300px;">
                    <canvas id="weaponsByStatusChart"></canvas>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-table"></i> بيانات الأسلحة بالأرقام</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>المستودع</th>
                                <th>عدد الأسلحة</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for item in data.weapon_counts_by_warehouse %}
                            <tr>
                                <td>{{ item.name }}</td>
                                <td>{{ item.count }}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>

                    <hr>

                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>نوع السلاح</th>
                                <th>العدد</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for item in data.weapon_counts_by_type %}
                            <tr>
                                <td>{{ item.type }}</td>
                                <td>{{ item.count }}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>

                    <hr>

                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>الحالة</th>
                                <th>العدد</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for item in data.weapon_counts_by_status %}
                            <tr>
                                <td>{{ item.status }}</td>
                                <td>{{ item.count }}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

{% elif stat_type == 'personnel' %}
<!-- Personnel Statistics -->
<div class="row">
    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-warehouse"></i> توزيع الأفراد حسب المستودع</h5>
            </div>
            <div class="card-body">
                <div class="chart-container" style="position: relative; height: 300px;">
                    <canvas id="personnelByWarehouseChart"></canvas>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-medal"></i> توزيع الأفراد حسب الرتبة</h5>
            </div>
            <div class="card-body">
                <div class="chart-container" style="position: relative; height: 300px;">
                    <canvas id="personnelByRankChart"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-chart-pie"></i> توزيع الأفراد حسب الحالة</h5>
            </div>
            <div class="card-body">
                <div class="chart-container" style="position: relative; height: 300px;">
                    <canvas id="personnelByStatusChart"></canvas>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-table"></i> بيانات الأفراد بالأرقام</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>المستودع</th>
                                <th>عدد الأفراد</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for item in data.personnel_counts_by_warehouse %}
                            <tr>
                                <td>{{ item.name }}</td>
                                <td>{{ item.count }}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>

                    <hr>

                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>الرتبة</th>
                                <th>العدد</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for item in data.personnel_counts_by_rank %}
                            <tr>
                                <td>{{ item.rank }}</td>
                                <td>{{ item.count }}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>

                    <hr>

                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>الحالة</th>
                                <th>العدد</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for item in data.personnel_counts_by_status %}
                            <tr>
                                <td>{{ item.status }}</td>
                                <td>{{ item.count }}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

{% elif stat_type == 'devices' %}
<!-- Device Statistics -->
<div class="row">
    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-map-marker-alt"></i> توزيع الأجهزة حسب الموقع</h5>
            </div>
            <div class="card-body">
                <div class="chart-container" style="position: relative; height: 300px;">
                    <canvas id="devicesByLocationChart"></canvas>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-desktop"></i> توزيع الأجهزة حسب النوع</h5>
            </div>
            <div class="card-body">
                <div class="chart-container" style="position: relative; height: 300px;">
                    <canvas id="devicesByTypeChart"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-chart-pie"></i> توزيع الأجهزة حسب الحالة</h5>
            </div>
            <div class="card-body">
                <div class="chart-container" style="position: relative; height: 300px;">
                    <canvas id="devicesByStatusChart"></canvas>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-table"></i> بيانات الأجهزة بالأرقام</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <div class="alert alert-info">
                        <strong>إجمالي عدد الأجهزة:</strong> {{ data.total_devices }}
                    </div>

                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>الموقع</th>
                                <th>عدد الأجهزة</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for item in data.device_counts_by_location %}
                            <tr>
                                <td>{{ item.name }}</td>
                                <td>{{ item.count }}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>

                    <hr>

                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>نوع الجهاز</th>
                                <th>العدد</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for item in data.device_counts_by_type %}
                            <tr>
                                <td>
                                    {% if item.type == 'computer' %}
                                    كمبيوتر
                                    {% elif item.type == 'laptop' %}
                                    لابتوب
                                    {% elif item.type == 'printer' %}
                                    طابعة
                                    {% elif item.type == 'scanner' %}
                                    سكانر
                                    {% elif item.type == 'monitor' %}
                                    شاشة
                                    {% elif item.type == 'projector' %}
                                    جهاز عرض
                                    {% elif item.type == 'server' %}
                                    سيرفر
                                    {% elif item.type == 'network' %}
                                    معدات شبكة
                                    {% elif item.type == 'camera' %}
                                    كاميرا
                                    {% elif item.type == 'ups' %}
                                    مزود طاقة احتياطي
                                    {% else %}
                                    أخرى
                                    {% endif %}
                                </td>
                                <td>{{ item.count }}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>

                    <hr>

                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>الحالة</th>
                                <th>العدد</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for item in data.device_counts_by_status %}
                            <tr>
                                <td>{{ item.status }}</td>
                                <td>{{ item.count }}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function () {
        // Chart.js Configuration
        const config = {
            responsive: true,
            maintainAspectRatio: false,
            legend: {
                position: 'right',
                labels: {
                    fontFamily: 'Cairo, Tajawal, sans-serif',
                    fontColor: document.body.classList.contains('light-theme') ? '#212529' : '#f0f0f0'
                }
            }
        };

        {% if stat_type == 'warehouses' %}
        // Warehouse Weapons Chart
        const warehouseLabels = [{% for warehouse in data.warehouse_stats %}'{{ warehouse.name }}'{% if not loop.last %}, {% endif %} {% endfor %}];
    const weaponsData = [{% for warehouse in data.warehouse_stats %}{{ warehouse.weapons_count }} {% if not loop.last %}, {% endif %} {% endfor %}];

    new Chart(document.getElementById('warehouseWeaponsChart').getContext('2d'), {
        type: 'bar',
        data: {
            labels: warehouseLabels,
            datasets: [{
                label: 'عدد الأسلحة',
                data: weaponsData,
                backgroundColor: '#007bff',
                borderColor: '#0062cc',
                borderWidth: 1
            }]
        },
        options: {
            ...config,
            scales: {
                yAxes: [{
                    ticks: {
                        beginAtZero: true
                    }
                }]
            }
        }
    });

    // Warehouse Personnel Chart
    const personnelData = [{% for warehouse in data.warehouse_stats %}{{ warehouse.personnel_count }} {% if not loop.last %}, {% endif %} {% endfor %}];

    new Chart(document.getElementById('warehousePersonnelChart').getContext('2d'), {
        type: 'bar',
        data: {
            labels: warehouseLabels,
            datasets: [{
                label: 'عدد الأفراد',
                data: personnelData,
                backgroundColor: '#28a745',
                borderColor: '#218838',
                borderWidth: 1
            }]
        },
        options: {
            ...config,
            scales: {
                yAxes: [{
                    ticks: {
                        beginAtZero: true
                    }
                }]
            }
        }
    });

    // Weapon Status Charts for each warehouse
    {% for warehouse in data.warehouse_stats %}
    new Chart(document.getElementById('weaponStatus{{ loop.index }}Chart').getContext('2d'), {
        type: 'doughnut',
        data: {
            labels: ['نشط', 'صيانة', 'إجازة', 'مهمة', 'تالف'],
            datasets: [{
                data: [
                    {{ warehouse.weapon_status.active }},
            {{ warehouse.weapon_status.maintenance }},
                        {{ warehouse.weapon_status.leave }},
        {{ warehouse.weapon_status.mission }},
        {{ warehouse.weapon_status.damaged }}
                    ],
        backgroundColor: [
        '#28a745', // نشط
        '#FAAFBE', // صيانة
        '#ffc107', // إجازة
        '#fd7e14', // مهمة
        '#dc3545'  // تالف
    ]
                }]
            },
        options: config
        });
    {% endfor %}

    {% elif stat_type == 'weapons' %}
    // Weapons by Warehouse Chart
    const warehouseLabels = [{% for item in data.weapon_counts_by_warehouse %}'{{ item.name }}'{% if not loop.last %}, {% endif %} {% endfor %}];
    const weaponsByWarehouseData = [{% for item in data.weapon_counts_by_warehouse %}{{ item.count }} {% if not loop.last %}, {% endif %} {% endfor %}];

    new Chart(document.getElementById('weaponsByWarehouseChart').getContext('2d'), {
        type: 'bar',
        data: {
            labels: warehouseLabels,
            datasets: [{
                label: 'عدد الأسلحة',
                data: weaponsByWarehouseData,
                backgroundColor: '#007bff',
                borderColor: '#0062cc',
                borderWidth: 1
            }]
        },
        options: {
            ...config,
            scales: {
                yAxes: [{
                    ticks: {
                        beginAtZero: true
                    }
                }]
            }
        }
    });

    // Weapons by Type Chart
    const typeLabels = [{% for item in data.weapon_counts_by_type %}'{{ item.type }}'{% if not loop.last %}, {% endif %} {% endfor %}];
    const weaponsByTypeData = [{% for item in data.weapon_counts_by_type %}{{ item.count }} {% if not loop.last %}, {% endif %} {% endfor %}];

    new Chart(document.getElementById('weaponsByTypeChart').getContext('2d'), {
        type: 'pie',
        data: {
            labels: typeLabels,
            datasets: [{
                data: weaponsByTypeData,
                backgroundColor: [
                    '#007bff',
                    '#28a745',
                    '#ffc107',
                    '#dc3545',
                    '#17a2b8',
                    '#6c757d',
                    '#343a40',
                    '#fd7e14'
                ]
            }]
        },
        options: config
    });

    // Weapons by Status Chart
    const statusLabels = [{% for item in data.weapon_counts_by_status %}'{{ item.status }}'{% if not loop.last %}, {% endif %} {% endfor %}];
    const weaponsByStatusData = [{% for item in data.weapon_counts_by_status %}{{ item.count }} {% if not loop.last %}, {% endif %} {% endfor %}];

    new Chart(document.getElementById('weaponsByStatusChart').getContext('2d'), {
        type: 'doughnut',
        data: {
            labels: statusLabels,
            datasets: [{
                data: weaponsByStatusData,
                backgroundColor: [
                    '#28a745', // نشط
                    '#FAAFBE', // صيانة
                    '#ffc107', // إجازة
                    '#fd7e14', // مهمة
                    '#dc3545', // تالف
                    '#6c757d'  // أخرى
                ]
            }]
        },
        options: config
    });

    {% elif stat_type == 'personnel' %}
    // Personnel by Warehouse Chart
    const warehouseLabels = [{% for item in data.personnel_counts_by_warehouse %}'{{ item.name }}'{% if not loop.last %}, {% endif %} {% endfor %}];
    const personnelByWarehouseData = [{% for item in data.personnel_counts_by_warehouse %}{{ item.count }} {% if not loop.last %}, {% endif %} {% endfor %}];

    new Chart(document.getElementById('personnelByWarehouseChart').getContext('2d'), {
        type: 'bar',
        data: {
            labels: warehouseLabels,
            datasets: [{
                label: 'عدد الأفراد',
                data: personnelByWarehouseData,
                backgroundColor: '#28a745',
                borderColor: '#218838',
                borderWidth: 1
            }]
        },
        options: {
            ...config,
            scales: {
                yAxes: [{
                    ticks: {
                        beginAtZero: true
                    }
                }]
            }
        }
    });

    // Personnel by Rank Chart
    const rankLabels = [{% for item in data.personnel_counts_by_rank %}'{{ item.rank }}'{% if not loop.last %}, {% endif %} {% endfor %}];
    const personnelByRankData = [{% for item in data.personnel_counts_by_rank %}{{ item.count }} {% if not loop.last %}, {% endif %} {% endfor %}];

    new Chart(document.getElementById('personnelByRankChart').getContext('2d'), {
        type: 'pie',
        data: {
            labels: rankLabels,
            datasets: [{
                data: personnelByRankData,
                backgroundColor: [
                    '#007bff',
                    '#28a745',
                    '#ffc107',
                    '#dc3545',
                    '#17a2b8',
                    '#6c757d',
                    '#343a40',
                    '#fd7e14'
                ]
            }]
        },
        options: config
    });

    // Personnel by Status Chart
    const statusLabels = [{% for item in data.personnel_counts_by_status %}'{{ item.status }}'{% if not loop.last %}, {% endif %} {% endfor %}];
    const personnelByStatusData = [{% for item in data.personnel_counts_by_status %}{{ item.count }} {% if not loop.last %}, {% endif %} {% endfor %}];

    new Chart(document.getElementById('personnelByStatusChart').getContext('2d'), {
        type: 'doughnut',
        data: {
            labels: statusLabels,
            datasets: [{
                data: personnelByStatusData,
                backgroundColor: [
                    '#28a745', // نشط
                    '#ffc107', // إجازة
                    '#fd7e14', // مهمة
                    '#dc3545', // دورة
                    '#007bff', // مستلم
                    '#C8BBBE'  // رماية
                ]
            }]
        },
        options: config
    });

    {% elif stat_type == 'devices' %}
    // Devices by Location Chart
    const locationLabels = [{% for item in data.device_counts_by_location %}'{{ item.name }}'{% if not loop.last %}, {% endif %} {% endfor %}];
    const devicesByLocationData = [{% for item in data.device_counts_by_location %}{{ item.count }} {% if not loop.last %}, {% endif %} {% endfor %}];

    new Chart(document.getElementById('devicesByLocationChart').getContext('2d'), {
        type: 'bar',
        data: {
            labels: locationLabels,
            datasets: [{
                label: 'عدد الأجهزة',
                data: devicesByLocationData,
                backgroundColor: '#17a2b8',
                borderColor: '#138496',
                borderWidth: 1
            }]
        },
        options: {
            ...config,
            scales: {
                yAxes: [{
                    ticks: {
                        beginAtZero: true
                    }
                }]
            }
        }
    });

    // Devices by Type Chart
    const typeLabels = [];
    const devicesByTypeData = [];

    {% for item in data.device_counts_by_type %}
    {% if item.type == 'computer' %}
    typeLabels.push('كمبيوتر');
    {% elif item.type == 'laptop' %}
    typeLabels.push('لابتوب');
    {% elif item.type == 'printer' %}
    typeLabels.push('طابعة');
    {% elif item.type == 'scanner' %}
    typeLabels.push('سكانر');
    {% elif item.type == 'monitor' %}
    typeLabels.push('شاشة');
    {% elif item.type == 'projector' %}
    typeLabels.push('جهاز عرض');
    {% elif item.type == 'server' %}
    typeLabels.push('سيرفر');
    {% elif item.type == 'network' %}
    typeLabels.push('معدات شبكة');
    {% elif item.type == 'camera' %}
    typeLabels.push('كاميرا');
    {% elif item.type == 'ups' %}
    typeLabels.push('مزود طاقة احتياطي');
    {% else %}
    typeLabels.push('أخرى');
    {% endif %}
    devicesByTypeData.push({{ item.count }});
    {% endfor %}

    new Chart(document.getElementById('devicesByTypeChart').getContext('2d'), {
        type: 'pie',
        data: {
            labels: typeLabels,
            datasets: [{
                data: devicesByTypeData,
                backgroundColor: [
                    '#0d6efd', // كمبيوتر
                    '#20c997', // لابتوب
                    '#28a745', // طابعة
                    '#ffc107', // سكانر
                    '#6f42c1', // شاشة
                    '#fd7e14', // جهاز عرض
                    '#dc3545', // سيرفر
                    '#17a2b8', // معدات شبكة
                    '#e83e8c', // كاميرا
                    '#6610f2', // مزود طاقة احتياطي
                    '#6c757d'  // أخرى
                ]
            }]
        },
        options: config
    });

    // Devices by Status Chart
    const statusLabels = [{% for item in data.device_counts_by_status %}'{{ item.status }}'{% if not loop.last %}, {% endif %} {% endfor %}];
    const devicesByStatusData = [{% for item in data.device_counts_by_status %}{{ item.count }} {% if not loop.last %}, {% endif %} {% endfor %}];

    new Chart(document.getElementById('devicesByStatusChart').getContext('2d'), {
        type: 'doughnut',
        data: {
            labels: statusLabels,
            datasets: [{
                data: devicesByStatusData,
                backgroundColor: [
                    '#28a745', // سليم
                    '#ffc107', // عطل بسيط
                    '#dc3545', // عطل جسيم
                    '#FAAFBE', // تحت الصيانة
                    '#6c757d', // خارج الخدمة
                    '#343a40'  // مفقود
                ]
            }]
        },
        options: config
    });
    {% endif %}
    });
</script>
{% endblock %}