/* Personnel Modal Styles */

.personnel-result {
    transition: all 0.3s ease;
    cursor: pointer;
}

.personnel-result:hover {
    background-color: #f8f9fa;
    border-color: #007bff !important;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.personnel-result.selected {
    background-color: #e3f2fd;
    border-color: #2196f3 !important;
    border-width: 2px;
}

/* Personnel Info Card */
.personnel-info-card {
    border: 2px solid var(--success-color);
    border-radius: 0.75rem;
    background-color: var(--bg-secondary);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.personnel-info-card .card-header {
    background: linear-gradient(135deg, var(--success-color), var(--accent-color));
    border-bottom: 1px solid var(--border-color);
    color: white;
    font-weight: 600;
}

.personnel-info-card .card-body {
    padding: 1.5rem;
    background-color: var(--bg-secondary);
    color: var(--text-primary);
}

/* Personnel Info Rows */
.personnel-info-card .row {
    margin-bottom: 0.75rem;
}

.personnel-info-card .row:last-child {
    margin-bottom: 0;
}

.personnel-info-card strong {
    color: var(--text-primary);
    font-weight: 600;
}

.personnel-info-card .badge {
    font-size: 0.85rem;
    padding: 0.4rem 0.8rem;
}

/* Status Badges */
.status-badge {
    font-size: 0.875rem;
    padding: 0.375rem 0.75rem;
    border-radius: 0.25rem;
}

/* Search Status */
.search-status {
    text-align: center;
    padding: 1rem;
}

.search-status .spinner {
    margin-right: 0.5rem;
}

/* Modal Improvements */
.modal-lg .modal-body {
    padding: 1.5rem;
    background-color: var(--bg-primary);
}

.modal-content {
    background-color: var(--bg-primary);
    border: 1px solid var(--border-color);
}

.modal-header {
    background: linear-gradient(135deg, var(--success-color), var(--accent-color));
    color: white;
    border-bottom: 1px solid var(--border-color);
}

.modal-header .btn-close {
    filter: invert(1);
}

.modal-footer {
    background-color: var(--bg-secondary);
    border-top: 1px solid var(--border-color);
}

/* Form Improvements */
.form-label {
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
}

.form-control {
    background-color: var(--bg-tertiary);
    border: 1px solid var(--border-color);
    color: var(--text-primary);
}

.form-control:focus {
    border-color: var(--accent-color);
    box-shadow: 0 0 0 0.2rem rgba(var(--accent-color), 0.25);
    background-color: var(--bg-tertiary);
    color: var(--text-primary);
}

.form-control::placeholder {
    color: var(--text-secondary);
}

.input-group .btn {
    border-color: var(--accent-color);
    color: var(--accent-color);
}

.input-group .btn:hover {
    background-color: var(--accent-color);
    border-color: var(--accent-color);
    color: white;
}

/* Alert Improvements */
.alert {
    border: 1px solid var(--border-color);
    border-radius: 0.5rem;
    font-weight: 500;
    background-color: var(--bg-secondary);
    color: var(--text-primary);
}

.alert-info {
    background-color: rgba(var(--info-color), 0.1);
    border-color: var(--info-color);
    color: var(--text-primary);
}

.alert-warning {
    background-color: rgba(var(--warning-color), 0.1);
    border-color: var(--warning-color);
    color: var(--text-primary);
}

.alert-danger {
    background-color: rgba(var(--danger-color), 0.1);
    border-color: var(--danger-color);
    color: var(--text-primary);
}

/* Button Improvements */
.btn-primary {
    background: linear-gradient(135deg, var(--accent-color), var(--accent-hover));
    border: none;
    font-weight: 500;
    transition: all 0.3s ease;
    color: white;
}

.btn-primary:hover {
    background: linear-gradient(135deg, var(--accent-hover), var(--accent-color));
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}

.btn-outline-primary {
    border-color: var(--accent-color);
    color: var(--accent-color);
    font-weight: 500;
    transition: all 0.3s ease;
    background-color: transparent;
}

.btn-outline-primary:hover {
    background-color: var(--accent-color);
    border-color: var(--accent-color);
    color: white;
    transform: translateY(-1px);
}

.btn-secondary {
    background-color: var(--bg-tertiary);
    border-color: var(--border-color);
    color: var(--text-primary);
}

.btn-secondary:hover {
    background-color: var(--border-color);
    border-color: var(--text-secondary);
    color: var(--text-primary);
}

/* Card Improvements */
.card {
    border: 1px solid var(--border-color);
    border-radius: 0.75rem;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    background-color: var(--bg-secondary);
}

.card:hover {
    box-shadow: 0 4px 20px rgba(0,0,0,0.15);
}

.card-header {
    border-radius: 0.75rem 0.75rem 0 0 !important;
    font-weight: 600;
    background-color: var(--bg-tertiary);
    border-bottom: 1px solid var(--border-color);
    color: var(--text-primary);
}

.card-body {
    background-color: var(--bg-secondary);
    color: var(--text-primary);
}

/* Badge Improvements */
.badge {
    font-size: 0.8rem;
    padding: 0.4rem 0.8rem;
    border-radius: 0.375rem;
    font-weight: 500;
}

/* Responsive Improvements */
@media (max-width: 768px) {
    .modal-lg {
        max-width: 95%;
        margin: 1rem auto;
    }
    
    .modal-body {
        padding: 1rem;
    }
    
    .row .col-md-6 {
        margin-bottom: 1rem;
    }
}

/* Animation for search results */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.search-result-enter {
    animation: fadeInUp 0.3s ease-out;
}

/* Loading spinner */
.spinner-border-sm {
    width: 1rem;
    height: 1rem;
}

/* Text improvements */
.text-muted {
    color: #6c757d !important;
}

.text-primary {
    color: #007bff !important;
}

/* Form text */
.form-text {
    font-size: 0.875rem;
    color: #6c757d;
    margin-top: 0.25rem;
}

/* Scrollbar for search results */
.search-results-container {
    scrollbar-width: thin;
    scrollbar-color: #007bff #f1f1f1;
}

.search-results-container::-webkit-scrollbar {
    width: 6px;
}

.search-results-container::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.search-results-container::-webkit-scrollbar-thumb {
    background: #007bff;
    border-radius: 3px;
}

.search-results-container::-webkit-scrollbar-thumb:hover {
    background: var(--accent-hover);
}

/* Light Theme Support */
body.light-theme .personnel-info-card {
    background-color: white;
    border-color: var(--light-success-color);
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

body.light-theme .personnel-info-card .card-header {
    background: linear-gradient(135deg, var(--light-success-color), var(--light-accent-color));
    color: white;
}

body.light-theme .personnel-info-card .card-body {
    background-color: white;
    color: var(--light-text-primary);
}

body.light-theme .personnel-info-card strong {
    color: var(--light-text-primary);
}

body.light-theme .modal-content {
    background-color: white;
    border-color: var(--light-border-color);
}

body.light-theme .modal-body {
    background-color: white;
}

body.light-theme .modal-footer {
    background-color: var(--light-bg-secondary);
    border-color: var(--light-border-color);
}

body.light-theme .form-control {
    background-color: white;
    border-color: var(--light-border-color);
    color: var(--light-text-primary);
}

body.light-theme .form-control:focus {
    background-color: white;
    border-color: var(--light-accent-color);
    color: var(--light-text-primary);
}

body.light-theme .form-control::placeholder {
    color: var(--light-text-secondary);
}

body.light-theme .form-label {
    color: var(--light-text-primary);
}

body.light-theme .card {
    background-color: white;
    border-color: var(--light-border-color);
}

body.light-theme .card-header {
    background-color: var(--light-bg-secondary);
    border-color: var(--light-border-color);
    color: var(--light-text-primary);
}

body.light-theme .card-body {
    background-color: white;
    color: var(--light-text-primary);
}

body.light-theme .alert {
    background-color: var(--light-bg-secondary);
    color: var(--light-text-primary);
    border-color: var(--light-border-color);
}

body.light-theme .btn-secondary {
    background-color: var(--light-bg-tertiary);
    border-color: var(--light-border-color);
    color: var(--light-text-primary);
}

body.light-theme .btn-secondary:hover {
    background-color: var(--light-border-color);
    color: var(--light-text-primary);
}
