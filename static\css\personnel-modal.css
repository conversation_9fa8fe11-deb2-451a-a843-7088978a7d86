/* Personnel Modal Styles */

.personnel-result {
    transition: all 0.3s ease;
    cursor: pointer;
}

.personnel-result:hover {
    background-color: #f8f9fa;
    border-color: #007bff !important;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.personnel-result.selected {
    background-color: #e3f2fd;
    border-color: #2196f3 !important;
    border-width: 2px;
}

/* Personnel Info Card */
.personnel-info-card {
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    background-color: #fff;
}

.personnel-info-card .card-header {
    background: linear-gradient(135deg, #007bff, #0056b3);
    border-bottom: 1px solid #dee2e6;
}

.personnel-info-card .card-body {
    padding: 1.25rem;
}

/* Status Badges */
.status-badge {
    font-size: 0.875rem;
    padding: 0.375rem 0.75rem;
    border-radius: 0.25rem;
}

/* Search Status */
.search-status {
    text-align: center;
    padding: 1rem;
}

.search-status .spinner {
    margin-right: 0.5rem;
}

/* Modal Improvements */
.modal-lg .modal-body {
    padding: 1.5rem;
}

.modal-header {
    background: linear-gradient(135deg, #007bff, #0056b3);
    color: white;
    border-bottom: none;
}

.modal-header .btn-close {
    filter: invert(1);
}

/* Form Improvements */
.form-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 0.5rem;
}

.form-control:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.input-group .btn {
    border-color: #007bff;
}

.input-group .btn:hover {
    background-color: #007bff;
    border-color: #007bff;
    color: white;
}

/* Alert Improvements */
.alert {
    border: none;
    border-radius: 0.5rem;
    font-weight: 500;
}

.alert-info {
    background: linear-gradient(135deg, #d1ecf1, #bee5eb);
    color: #0c5460;
}

.alert-warning {
    background: linear-gradient(135deg, #fff3cd, #ffeaa7);
    color: #856404;
}

.alert-danger {
    background: linear-gradient(135deg, #f8d7da, #f5c6cb);
    color: #721c24;
}

/* Button Improvements */
.btn-primary {
    background: linear-gradient(135deg, #007bff, #0056b3);
    border: none;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #0056b3, #004085);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}

.btn-outline-primary {
    border-color: #007bff;
    color: #007bff;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-outline-primary:hover {
    background-color: #007bff;
    border-color: #007bff;
    transform: translateY(-1px);
}

/* Card Improvements */
.card {
    border: none;
    border-radius: 0.75rem;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.card:hover {
    box-shadow: 0 4px 20px rgba(0,0,0,0.15);
}

.card-header {
    border-radius: 0.75rem 0.75rem 0 0 !important;
    font-weight: 600;
}

/* Badge Improvements */
.badge {
    font-size: 0.8rem;
    padding: 0.4rem 0.8rem;
    border-radius: 0.375rem;
    font-weight: 500;
}

/* Responsive Improvements */
@media (max-width: 768px) {
    .modal-lg {
        max-width: 95%;
        margin: 1rem auto;
    }
    
    .modal-body {
        padding: 1rem;
    }
    
    .row .col-md-6 {
        margin-bottom: 1rem;
    }
}

/* Animation for search results */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.search-result-enter {
    animation: fadeInUp 0.3s ease-out;
}

/* Loading spinner */
.spinner-border-sm {
    width: 1rem;
    height: 1rem;
}

/* Text improvements */
.text-muted {
    color: #6c757d !important;
}

.text-primary {
    color: #007bff !important;
}

/* Form text */
.form-text {
    font-size: 0.875rem;
    color: #6c757d;
    margin-top: 0.25rem;
}

/* Scrollbar for search results */
.search-results-container {
    scrollbar-width: thin;
    scrollbar-color: #007bff #f1f1f1;
}

.search-results-container::-webkit-scrollbar {
    width: 6px;
}

.search-results-container::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.search-results-container::-webkit-scrollbar-thumb {
    background: #007bff;
    border-radius: 3px;
}

.search-results-container::-webkit-scrollbar-thumb:hover {
    background: #0056b3;
}
