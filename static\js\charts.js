/**
 * Charts.js - Chart generation functionality for Military Warehouse Management System
 * Using Chart.js library for visualization
 */

// Wait for the DOM to be fully loaded
document.addEventListener("DOMContentLoaded", function () {
  // Initialize any charts present on the page
  initializeCharts();
});

/**
 * Initialize all charts on the page
 */
function initializeCharts() {
  // Only initialize charts if their elements exist
  if (document.getElementById('weaponStatusChart')) {
    initializeWeaponStatusChart();
  }
  if (document.getElementById('personnelStatusChart')) {
    initializePersonnelStatusChart();
  }
  if (document.getElementById('transactionsChart')) {
    initializeTransactionsChart();
  }
  if (document.getElementById('comparisonChart')) {
    initializeComparisonChart();
  }
}

/**
 * Initialize the weapon status chart
 * @param {string} chartId - The ID of the chart canvas element
 * @param {Object} data - The data for the chart
 */
function initializeWeaponStatus<PERSON>hart(
  chartId = "weaponStatusChart",
  data = null
) {
  const chartElement = document.getElementById(chartId);
  if (!chartElement) return;

  // If data wasn't provided, try to get it from the data attribute
  if (!data && chartElement.dataset.chartData) {
    try {
      data = JSON.parse(chartElement.dataset.chartData);
    } catch (e) {
      console.error("Invalid chart data", e);
      return;
    }
  }

  // Default data if none provided
  if (!data) {
    data = {
      active: 0,
      leave: 0,
      mission: 0,
      maintenance: 0,
      damaged: 0,
      other: 0,
    };
  }

  // Set up the chart configuration
  const config = {
    type: "doughnut",
    data: {
      labels: ["نشط", "إجازة", "مهمة", "صيانة", "تالف", "أخرى"],
      datasets: [
        {
          data: [
            data.active,
            data.leave,
            data.mission,
            data.maintenance,
            data.damaged,
            data.other,
          ],
          backgroundColor: [
            "#28a745", // Green for active
            "#ffc107", // Yellow for leave
            "#fd7e14", // Orange for mission
            "#FAAFBE", // Pink for maintenance
            "#dc3545", // Red for damaged/cycle
            "#6c757d", // Gray for other/vacant
          ],
          borderWidth: 1,
        },
      ],
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      legend: {
        position: "right",
        labels: {
          fontFamily: "Cairo, Tajawal, sans-serif",
          fontColor: document.body.classList.contains("light-theme")
            ? "#212529"
            : "#f0f0f0",
        },
      },
      tooltips: {
        callbacks: {
          label: function (tooltipItem, data) {
            const dataset = data.datasets[tooltipItem.datasetIndex];
            const total = dataset.data.reduce((acc, value) => acc + value, 0);
            const value = dataset.data[tooltipItem.index];
            const percentage = Math.round((value / total) * 100 * 10) / 10;
            return `${
              data.labels[tooltipItem.index]
            }: ${value} (${percentage}%)`;
          },
        },
      },
    },
  };

  // Create the chart
  new Chart(chartElement, config);
}

/**
 * Initialize the personnel status chart
 * @param {string} chartId - The ID of the chart canvas element
 * @param {Object} data - The data for the chart
 */
function initializePersonnelStatusChart(
  chartId = "personnelStatusChart",
  data = null
) {
  const chartElement = document.getElementById(chartId);
  if (!chartElement) return;

  // تدمير أي رسم بياني موجود مسبقاً لنفس العنصر
  Chart.helpers.each(Chart.instances, function(instance) {
    if (instance.canvas.id === chartId) {
      instance.destroy();
    }
  });

  // If data wasn't provided, try to get it from the data attribute
  if (!data && chartElement.dataset.chartData) {
    try {
      data = JSON.parse(chartElement.dataset.chartData);
    } catch (e) {
      console.error("Invalid chart data", e);
      return;
    }
  }

  // Default data if none provided - updated to include all 6 statuses
  if (!data) {
    data = {
      active: 0,
      leave: 0,
      mission: 0,
      cycle: 0,
      retired: 0,
      shooting: 0,
    };
  }

  // Set up the chart configuration
  const config = {
    type: "doughnut",
    data: {
      labels: ["نشط", "إجازة", "مهمة", "دورة", "مستلم", "رماية"],
      datasets: [
        {
          data: [data.active, data.leave, data.mission, data.cycle, data.retired, data.shooting],
          backgroundColor: [
            "#28a745", // Green for active
            "#ffc107", // Yellow for leave
            "#fd7e14", // Orange for mission
            "#dc3545", // Red for cycle
            "#007bff", // Blue for retired (مستلم)
            "#C8BBBE", // Light purple for shooting
          ],
          borderWidth: 1,
        },
      ],
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          position: "right",
          labels: {
            font: {
              family: "Cairo, Tajawal, sans-serif"
            },
            color: document.body.classList.contains("light-theme")
              ? "#212529"
              : "#f0f0f0",
          },
        },
        tooltip: {
          callbacks: {
            label: function (context) {
              const dataset = context.dataset;
              const total = dataset.data.reduce((acc, value) => acc + value, 0);
              const value = dataset.data[context.dataIndex];
              const percentage = Math.round((value / total) * 100 * 10) / 10;
              return `${context.label}: ${value} (${percentage}%)`;
            },
          },
        },
      },
    },
  };

  // Create the chart
  new Chart(chartElement, config);
}

/**
 * Initialize the transactions chart showing activity over time
 * @param {string} chartId - The ID of the chart canvas element
 * @param {Object} data - The data for the chart
 */
function initializeTransactionsChart(
  chartId = "transactionsChart",
  data = null
) {
  const chartElement = document.getElementById(chartId);
  if (!chartElement) return;

  // If data wasn't provided, try to get it from the data attribute
  if (!data && chartElement.dataset.chartData) {
    try {
      data = JSON.parse(chartElement.dataset.chartData);
    } catch (e) {
      console.error("Invalid chart data", e);
      return;
    }
  }

  // Default data if none provided
  if (!data || !data.labels || !data.datasets) {
    data = {
      labels: [],
      datasets: [
        {
          label: "استلام",
          data: [],
          borderColor: "#28a745",
          backgroundColor: "rgba(40, 167, 69, 0.1)",
          fill: true,
        },
        {
          label: "تسليم",
          data: [],
          borderColor: "#dc3545",
          backgroundColor: "rgba(220, 53, 69, 0.1)",
          fill: true,
        },
        {
          label: "نقل",
          data: [],
          borderColor: "#007bff",
          backgroundColor: "rgba(0, 123, 255, 0.1)",
          fill: true,
        },
      ],
    };
  }

  // Set up the chart configuration
  const config = {
    type: "line",
    data: data,
    options: {
      responsive: true,
      maintainAspectRatio: false,
      scales: {
        yAxes: [
          {
            ticks: {
              beginAtZero: true,
              fontColor: document.body.classList.contains("light-theme")
                ? "#212529"
                : "#f0f0f0",
            },
            gridLines: {
              color: document.body.classList.contains("light-theme")
                ? "rgba(0,0,0,0.1)"
                : "rgba(255,255,255,0.1)",
            },
          },
        ],
        xAxes: [
          {
            ticks: {
              fontColor: document.body.classList.contains("light-theme")
                ? "#212529"
                : "#f0f0f0",
            },
            gridLines: {
              color: document.body.classList.contains("light-theme")
                ? "rgba(0,0,0,0.1)"
                : "rgba(255,255,255,0.1)",
            },
          },
        ],
      },
      legend: {
        position: "top",
        labels: {
          fontFamily: "Cairo, Tajawal, sans-serif",
          fontColor: document.body.classList.contains("light-theme")
            ? "#212529"
            : "#f0f0f0",
        },
      },
    },
  };

  // Create the chart
  new Chart(chartElement, config);
}

/**
 * Initialize a warehouse comparison chart for comparing stats between warehouses
 * @param {string} chartId - The ID of the chart canvas element
 * @param {Object} data - The data for the chart
 */
function initializeComparisonChart(
  data = null,
  chartId = "warehouseComparisonChart"
) {
  const chartElement = document.getElementById(chartId);
  if (!chartElement) return;

  // If data wasn't provided, try to get it from the data attribute
  if (!data && chartElement.dataset.chartData) {
    try {
      data = JSON.parse(chartElement.dataset.chartData);
    } catch (e) {
      console.error("Invalid chart data", e);
      return;
    }
  }

  // Default data if none provided
  if (!data || !data.labels || !data.datasets) {
    data = {
      labels: ["المستودع الأول", "المستودع الثاني", "المخزون العام"],
      datasets: [
        {
          label: "الأسلحة",
          data: [0, 0, 0],
          backgroundColor: "rgba(0, 123, 255, 0.7)",
        },
        {
          label: "الأفراد",
          data: [0, 0, 0],
          backgroundColor: "rgba(40, 167, 69, 0.7)",
        },
        {
          label: "الأجهزة",
          data: [0, 0, 0],
          backgroundColor: "rgba(255, 193, 7, 0.7)",
        },
      ],
    };
  }

  // Set up the chart configuration
  const config = {
    type: "bar",
    data: data,
    options: {
      responsive: true,
      maintainAspectRatio: false,
      scales: {
        yAxes: [
          {
            ticks: {
              beginAtZero: true,
              fontColor: document.body.classList.contains("light-theme")
                ? "#212529"
                : "#f0f0f0",
            },
            gridLines: {
              color: document.body.classList.contains("light-theme")
                ? "rgba(0,0,0,0.1)"
                : "rgba(255,255,255,0.1)",
            },
          },
        ],
        xAxes: [
          {
            ticks: {
              fontColor: document.body.classList.contains("light-theme")
                ? "#212529"
                : "#f0f0f0",
            },
            gridLines: {
              color: document.body.classList.contains("light-theme")
                ? "rgba(0,0,0,0.1)"
                : "rgba(255,255,255,0.1)",
            },
          },
        ],
      },
      legend: {
        position: "top",
        labels: {
          fontFamily: "Cairo, Tajawal, sans-serif",
          fontColor: document.body.classList.contains("light-theme")
            ? "#212529"
            : "#f0f0f0",
        },
      },
    },
  };

  // Create the chart
  new Chart(chartElement, config);
}

/**
 * Update chart theme based on the current site theme
 */
function updateChartsTheme() {
  const isLightTheme = document.body.classList.contains("light-theme");
  const fontColor = isLightTheme ? "#212529" : "#f0f0f0";
  const gridColor = isLightTheme ? "rgba(0,0,0,0.1)" : "rgba(255,255,255,0.1)";

  Chart.helpers.each(Chart.instances, function (chart) {
    // Update legend font color
    if (chart.options.legend && chart.options.legend.labels) {
      chart.options.legend.labels.fontColor = fontColor;
    }

    // Update axis colors
    if (chart.options.scales) {
      // Update y-axis
      if (chart.options.scales.yAxes) {
        for (let i = 0; i < chart.options.scales.yAxes.length; i++) {
          if (chart.options.scales.yAxes[i].ticks) {
            chart.options.scales.yAxes[i].ticks.fontColor = fontColor;
          }
          if (chart.options.scales.yAxes[i].gridLines) {
            chart.options.scales.yAxes[i].gridLines.color = gridColor;
          }
        }
      }

      // Update x-axis
      if (chart.options.scales.xAxes) {
        for (let i = 0; i < chart.options.scales.xAxes.length; i++) {
          if (chart.options.scales.xAxes[i].ticks) {
            chart.options.scales.xAxes[i].ticks.fontColor = fontColor;
          }
          if (chart.options.scales.xAxes[i].gridLines) {
            chart.options.scales.xAxes[i].gridLines.color = gridColor;
          }
        }
      }
    }

    chart.update();
  });
}

// Listen for theme changes to update charts accordingly
document.addEventListener("themeChanged", function () {
  updateChartsTheme();
});
